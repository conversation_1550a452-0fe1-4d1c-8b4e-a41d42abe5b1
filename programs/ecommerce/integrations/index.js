import _ from 'lodash';

import enterstore from './enterstore';
import shopify from './shopify';
import trendyol from './trendyol';
import hepsiburada from './hepsiburada';
import pazarama from './pazarama';
import pttavm from './pttavm';
import n11 from './n11';
import ciceksepeti from './ciceksepeti';
import mirakl from './mirakl';
import farmazon from './farmazon';
import importReturns from './shopify/import-returns';

const INTEGRATIONS_MAP = {
    enterstore: enterstore,
    shopify: shopify,
    trendyol: trendyol,
    hepsiburada: hepsiburada,
    pazarama: pazarama,
    pttavm: pttavm,
    n11: n11,
    ciceksepeti: ciceksepeti,
    koctas: mirakl,
    farmazon: farmazon
};

export default {
    async downloadCategories(app, storeId, onProgress) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.downloadCategories) {
            return await integration.downloadCategories(app, store, onProgress);
        }
    },

    async downloadBrands(app, storeId, onProgress) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.downloadBrands) {
            return await integration.downloadBrands(app, store, onProgress);
        }
    },

    async getAttributes(app, storeId, categoryId) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.getAttributes) {
            return await integration.getAttributes(app, store, categoryId);
        }
    },

    async importProducts(app, storeId, onProgress) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.importProducts) {
            return await integration.importProducts(app, store, onProgress);
        }
    },

    async syncProductStatuses(app, storeId) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.syncProductStatuses) {
            return await integration.syncProductStatuses(app, store);
        }
    },

    async saveProducts(app, storeId, items) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && integration.saveProducts) {
            return await integration.saveProducts(app, store, items);
        }
    },

    async deleteProducts(app, storeId, items) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && integration.deleteProducts) {
            return await integration.deleteProducts(app, store, items);
        }
    },

    async saveProductsStock(app, storeId, items) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && integration.saveProductsStock) {
            await integration.saveProductsStock(app, store, items);
        }
    },

    async saveProductsPrice(app, storeId, items) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && integration.saveProductsPrice) {
            await integration.saveProductsPrice(app, store, items);
        }
    },

    async importOrders(app, storeId, onProgress, startDate, endDate) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.importOrders) {
            return await integration.importOrders(app, store, onProgress, startDate, endDate);
        }
    },

    async importCanceledOrders(app, storeId, startDate, endDate) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.importCanceledOrders) {
            return await integration.importCanceledOrders(app, store, startDate, endDate);
        }
    },

    async importReturns(app, storeId, startDate, endDate) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.importReturns) {
            return await integration.importReturns(app, store, startDate, endDate);
        }
    },

    async updateOrderStatus(app, storeId, documentCollection, documentId, status) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.updateOrderStatus) {
            return await integration.updateOrderStatus(app, store, documentCollection, documentId, status);
        }
    },

    async sendInvoiceLink(app, storeId, invoiceId) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.sendInvoiceLink) {
            return await integration.sendInvoiceLink(app, store, invoiceId);
        }
    },

    async getCarriers(app, storeId) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.getCarriers) {
            return await integration.getCarriers(app, store);
        }
    },

    async changeCarrier(app, storeId, orderId, carrierId) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.changeCarrier) {
            return await integration.changeCarrier(app, store, orderId, carrierId);
        }
    },

    async updateDeliveryInfo(app, storeId, payload) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.updateDeliveryInfo) {
            return await integration.updateDeliveryInfo(app, store, payload);
        }
    },

    async checkStoreTransactionStatuses(app, storeId) {
        const store = await app.collection('ecommerce.stores').findOne({_id: storeId});
        const integration = INTEGRATIONS_MAP[store.integrationType];

        if (!!integration && !!integration.checkStoreTransactionStatuses) {
            return await integration.checkStoreTransactionStatuses(app, store);
        }
    }
};
