<template>
    <ui-view
        type="form"
        collection="finance.cheques"
        :method="saveMethod"
        :model="model"
        :extra-fields="['currencyRate', 'isIssued']"
        :title="title"
        :before-init="beforeInit"
        :before-submit="beforeSubmit"
        no-inner-padding
        full-height
        actions="onlyEdit"
        @changed="handleChange"
        v-if="initialized"
    >
        <el-tabs v-model="activeTab" class="full-tabs">
            <el-tab-pane name="general" :label="'GENERAL' | t">
                <el-scrollbar class="p20">
                    <div class="columns">
                        <div class="column is-half">
                            <ui-legend title="General" />
                            <ui-field name="code" disabled />
                            <ui-field name="status" :options="statusOptions" translate-labels disabled />
                            <ui-field name="chequeNumber" />
                            <ui-field
                                name="partnerType"
                                :options="partnerTypeOptions"
                                translate-labels
                                :disabled="!!$params('id')"
                            />
                            <ui-field
                                :key="partnerIdKey"
                                name="partnerId"
                                collection="kernel.partners"
                                view="partners.partners"
                                :extra-fields="['code']"
                                :template="'{{code}} - {{name}}'"
                                :label="partnerLabel"
                                :filters="{type: model.partnerType}"
                                :update-params="updatePartnerSelectParams"
                                :disabled="!!$params('id') || !model.partnerType"
                            />
                            <ui-field
                                name="contactPersonId"
                                collection="kernel.contacts"
                                view="partners.contacts"
                                :filters="{partnerId: model.partnerId, type: 'contact'}"
                                :update-params="updateContactSelectParams"
                                :disabled="!model.partnerId"
                            />
                            <template v-if="$setting('system.multiCurrency')">
                                <div class="ui-inline-fields">
                                    <div class="field-label">{{ 'Amount' | t }}</div>
                                    <div class="field-content">
                                        <ui-field
                                            name="amount"
                                            :precision="$setting('system.currencyPrecision')"
                                            label="hide"
                                            :disabled="!!$params('id')"
                                        />
                                        <ui-field
                                            name="currencyId"
                                            collection="kernel.currencies"
                                            label="hide"
                                            :disabled="!!$params('id')"
                                        />
                                    </div>
                                </div>
                            </template>
                            <template v-else>
                                <ui-field
                                    name="amount"
                                    :precision="$setting('system.currencyPrecision')"
                                    :disabled="!!$params('id')"
                                />
                                <ui-field
                                    name="currencyId"
                                    collection="kernel.currencies"
                                    :disabled="!!$params('id')"
                                    v-show="$setting('system.multiCurrency')"
                                />
                            </template>
                            <ui-field
                                name="journalId"
                                collection="accounting.journals"
                                :filters="journalIdFilters"
                                disabled
                            />
                            <ui-field
                                name="paymentAccountId"
                                collection="accounting.journals"
                                view="accounting.configuration.journals"
                                disable-create
                                disable-detail
                                :filters="paymentAccountIdFilters"
                            />
                            <ui-field name="countryId" collection="kernel.countries" disabled />
                            <ui-field name="endorsable" />
                            <ui-field name="cashFlowItemId" v-bind="cashFlowItemIdAttributes" />
                            <ui-field
                                name="financialProjectId"
                                collection="kernel.financial-projects"
                                view="system.management.configuration.financial-projects"
                                :filters="financialProjectIdFilters"
                                disable-detail
                                disable-create
                                :extra-fields="['code']"
                                :template="'{{code}} - {{name}}'"
                            />

                            <ui-legend title="Exchange Rates" class="mt30" />
                            <ui-field
                                name="exchangeRates"
                                class="mb0"
                                style="width: 450px"
                                :schema="exchangeRatesSchema"
                                :min-empty-rows="0"
                                :enable-add-remove="false"
                                :enable-row-handle="false"
                            />
                        </div>

                        <div class="column is-half">
                            <ui-legend title="Details" />
                            <kernel-common-branch-select disabled />
                            <ui-field name="recordDate" :disabled="!!$params('id')" />
                            <ui-field name="issueDate" :disabled="!!$params('id')" />
                            <ui-field
                                name="dueDate"
                                :disabled="!(status === 'in-bill-case' || status === 'produced')"
                            />
                            <ui-field name="reference" />
                            <ui-field name="description" />
                            <ui-field
                                name="scope"
                                :options="scopeOptions"
                                disabled
                                v-show="$setting('system.scopes')"
                            />
                            <ui-field name="firstWrittenBy" />
                            <ui-field name="editedBy" />
                            <ui-field name="financialIdentifier" />
                            <ui-field name="guaranteeId" />
                            <ui-field
                                name="issuedBy"
                                collection="kernel.partners"
                                view="partners.partners"
                                :filters="{type: 'employee'}"
                                :extra-fields="['code']"
                                :template="'{{code}} - {{name}}'"
                            />
                            <ui-field name="chequeTrackingCode" disabled />
                            <ui-field
                                name="bankId"
                                collection="kernel.banks"
                                view="system.management.configuration.banks"
                                disable-detail
                                disable-create
                                :disabled="!!journal.chequesIssued"
                            />
                            <ui-field
                                name="bankBranchName"
                                :fetch-suggestions="fetchBankBranchSuggestions"
                                field-type="autocomplete"
                                :disabled="!model.bankId"
                            />
                            <ui-field name="accountNumber" />
                            <ui-field name="iban" />
                            <ui-field
                                name="city"
                                :fetch-suggestions="fetchSuggestions"
                                :extra-params="{code: 'city'}"
                                field-type="autocomplete"
                            />
                            <ui-field
                                name="district"
                                :fetch-suggestions="fetchSuggestions"
                                :extra-params="{code: 'district'}"
                                field-type="autocomplete"
                            />
                            <ui-field
                                name="endorsedPartnerId"
                                collection="kernel.partners"
                                :extra-fields="['code']"
                                :template="'{{code}} - {{name}}'"
                                disabled
                                v-show="!!model.endorsedPartnerId"
                            />
                            <ui-field
                                name="salespersonId"
                                collection="kernel.partners"
                                view="partners.partners"
                                :filters="{type: 'employee'}"
                                :extra-fields="['code']"
                                disable-create
                                disable-detail
                                :template="'{{code}} - {{name}}'"
                            />
                            <ui-field name="tagIds" :options="tagOptions" />
                        </div>
                    </div>
                </el-scrollbar>
            </el-tab-pane>

            <el-tab-pane name="history" :label="'HISTORY' | t" :disabled="!$params('id')" lazy>
                <ui-table
                    ref="table"
                    id="finance.banking.cheque.item-history"
                    collection="finance.cheques-history"
                    :extra-fields="['journalEntryId']"
                    :columns="historyTableColumns"
                    :filters="{chequeId: $params('id'), $sort: {no: 1}}"
                    :enable-selection="false"
                    v-if="!!$params('id')"
                />
            </el-tab-pane>

            <el-tab-pane name="attachments" :label="'ATTACHMENTS' | t">
                <ui-field name="attachments" field-type="attachments" />
            </el-tab-pane>
        </el-tabs>
    </ui-view>
</template>

<script>
import _ from 'lodash';
import {getColumns} from './utils';
import {escapeRegExp, toLower, toUpper, firstUpper} from 'framework/helpers';

export default {
    data: () => ({
        model: {},
        journal: null,
        activeTab: 'general',
        statusOptions: [
            {value: 'in-bill-case', label: 'In bill case'},
            {value: 'deposited', label: 'Deposited'},
            {value: 'endorsed', label: 'Endorsed'},
            {value: 'charged', label: 'Charged'},
            {value: 'denied', label: 'Denied'},
            {value: 'refunded', label: 'Refunded'},
            {value: 'sent-in-the-bailiffs', label: 'Sent in the bailiffs'},
            {value: 'given-guarantee', label: 'Given guarantee'},
            {value: 'produced', label: 'Produced'},
            {value: 'paid', label: 'Paid'},
            {value: 'issued-refunded', label: 'Refunded'}
        ],
        partnerTypeOptions: [
            {value: 'customer', label: 'Customer'},
            {value: 'vendor', label: 'Vendor'},
            {value: 'employee', label: 'Employee'}
        ],
        partnerIdKey: _.uniqueId('partnerIdKey'),
        bank: null,
        isEndorsable: null,
        tagOptions: [],
        initialized: false
    }),

    computed: {
        saveMethod() {
            return this.model.isIssued ? 'finance.save-issued-cheque' : 'finance.save-cheque';
        },
        title() {
            const model = this.model;

            if (this.$params('id')) {
                return model.code ? model.code : '';
            }

            return model.code ? model.code : this.$t('New Cheque');
        },
        status() {
            const model = this.model;

            return model.status || 'in-bill-case';
        },
        partnerLabel() {
            const model = this.model;

            if (model.partnerType === 'customer') return 'Customer';
            else if (model.partnerType === 'vendor') return 'Vendor';
            else if (model.partnerType === 'employee') return 'Employee';
        },
        historyTableColumns() {
            let columns = _.cloneDeep(getColumns(this))
                .filter(c => {
                    if (c.field === 'dueDate') {
                        delete c.sort;
                    }

                    if (c.field === 'recordDate' || c.field === 'code' || c.field === 'description') {
                        c.visible = true;
                    }

                    return (
                        [
                            'dueDate',
                            'recordDate',
                            'issueDate',
                            'code',
                            'journal.name',
                            'partner.name',
                            'reference',
                            'description',
                            'amount',
                            'systemCurrencyAmount'
                        ].indexOf(c.field) !== -1
                    );
                })
                .map(c => {
                    if (c.field === 'journal.name') {
                        c.visible = true;
                    }

                    if (c.field === 'reference' || c.field === 'issueDate') {
                        c.visible = false;
                    }

                    return c;
                });

            columns.unshift({
                field: 'no',
                label: 'Order',
                width: 90,
                sort: 'asc'
            });
            columns.push({
                field: 'status',
                label: 'Status',
                valueLabels: this.statusOptions
            });
            columns.push({
                field: 'createdAt',
                label: 'Created at',
                format: 'datetime',
                width: 150
            });
            columns.push({
                field: 'paymentAccount',
                label: 'Payment account',
                subSelect: ['name'],
                width: 180,
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible = _.isObject(data.paymentAccount) && _.isString(data.paymentAccount.name);

                    if (relation.isVisible) {
                        relation.view = 'accounting.configuration.journals';
                        relation.id = data.paymentAccountId;
                        relation.template = '{{name}}';
                    }

                    return relation;
                }
            });
            columns.push({
                field: 'editedByInfo',
                label: 'Edited by',
                subSelect: ['code', 'name'],
                width: 240,
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible =
                        _.isObject(data.editedByInfo) &&
                        _.isString(data.editedByInfo.code) &&
                        _.isString(data.editedByInfo.name);

                    if (relation.isVisible) {
                        relation.view = 'system.members.users-detail';
                        relation.id = data.editedBy;
                        relation.template = '{{code}} - {{name}}';
                    }

                    return relation;
                }
            });

            columns = columns.map(c => {
                c.sortable = false;

                return c;
            });

            return columns;
        },
        journalIdFilters() {
            const multiCurrency = this.$setting('system.multiCurrency');

            if (multiCurrency) {
                return {
                    type: 'cheque',
                    branchId: this.model.branchId,
                    $or: [
                        {currencyId: {$exists: false}},
                        {currencyId: ''},
                        {currencyId: null},
                        {currencyId: this.model.currencyId}
                    ]
                };
            } else {
                return {type: 'cheque', branchId: this.model.branchId};
            }
        },
        paymentAccountIdFilters() {
            const multiCurrency = this.$setting('system.multiCurrency');
            const branchId = this.model.branchId;
            const query = {
                $disableBranchCheck: true,
                $and: []
            };

            const firstQuery = {$or: []};
            firstQuery.$or.push({
                type: 'cash-safe',
                branchId
            });
            firstQuery.$or.push({
                type: 'cash-safe',
                branchId: {$eq: null}
            });
            firstQuery.$or.push({
                type: 'cash-safe',
                branchId: {$exists: false}
            });
            if (this.model.scope !== '2') {
                firstQuery.$or.push({
                    type: 'bank'
                });
            }
            query.$and.push(firstQuery);

            if (multiCurrency) {
                const secondQuery = {$or: []};

                secondQuery.$or.push({currencyId: {$exists: false}}, {currencyId: ''});
                secondQuery.$or.push({currencyId: null});
                secondQuery.$or.push({currencyId: this.model.currencyId});

                query.$and.push(secondQuery);
            }

            return query;
        },
        cashFlowItemIdAttributes() {
            const self = this;

            const filters = {
                'tree.hasChild': {$ne: true},
                $sort: {
                    code: 1
                }
            };

            return {
                collection: 'finance.cash-flow-items',
                view: 'finance.configuration.cash-flow-items',
                filters,
                disableCreate: true,
                disableDetail: true,
                updateParams(params, type) {
                    params.isRowSelectable = row => {
                        return !row.tree.hasChild;
                    };

                    delete params.filters;

                    return params;
                },
                extraFields: ['code', 'name'],
                template: '{{code}} - {{name}}'
            };
        },
        financialProjectIdFilters() {
            return {
                $and: [
                    {
                        $or: [
                            {validFrom: {$exists: false}},
                            {validFrom: {$eq: null}},
                            {validFrom: {$lte: this.model.issueDate}}
                        ]
                    },
                    {
                        $or: [
                            {validTo: {$exists: false}},
                            {validTo: {$eq: null}},
                            {validTo: {$gte: this.model.issueDate}}
                        ]
                    }
                ],
                $sort: {code: 1}
            };
        },
        exchangeRatesSchema() {
            return {
                currencyName: {
                    type: 'string',
                    label: 'Currency',
                    required: false,
                    column: {
                        width: 150
                    },
                    editor: {
                        disabled: true
                    }
                },
                rate: {
                    type: 'decimal',
                    label: 'Rate',
                    default: 0,
                    column: {
                        format: 'amount',
                        formatOptions: () => {
                            return {
                                number: {
                                    precision: this.$setting('system.exchangeRatePrecision')
                                }
                            };
                        }
                    },
                    editor: {}
                },
                dueRate: {
                    type: 'decimal',
                    label: 'Due rate',
                    default: 0,
                    column: {
                        format: 'amount',
                        formatOptions: () => {
                            return {
                                number: {
                                    precision: this.$setting('system.exchangeRatePrecision')
                                }
                            };
                        }
                    },
                    editor: {}
                }
            };
        },
        scopeOptions() {
            return [
                {value: '1', label: this.$setting('system.scope1Label')},
                {value: '2', label: this.$setting('system.scope2Label')}
            ];
        }
    },

    methods: {
        async beforeInit(model) {
            const company = this.$store.getters['session/company'];

            // Default code.
            if (!model.code) {
                model.code = await this.$rpc('kernel.common.request-number', {
                    numberingId: this.journal.numberingId,
                    save: false
                });
            }

            // Default currency.
            if (!model.currencyId) {
                if (this.journal.currencyId) {
                    model.currencyId = this.journal.currencyId;
                } else {
                    model.currencyId = company.currencyId;
                }
            }

            if (!model.branchId && !!this.journal.branchId) {
                model.branchId = this.journal.branchId;
            }

            // Default currency.
            if (!model.countryId) model.countryId = company.address.countryId;

            // Default description.
            if (!model.description) {
                if (!this.journal.chequesIssued) {
                    model.description = this.$t('Receipts');
                } else {
                    model.description = this.$t('Payments');
                }
            }

            if (!model.bankId && this.journal.chequesIssued && !!this.journal.chequeBankAccountId) {
                const bankAccount = await this.$collection('accounting.bank-accounts').findOne({
                    _id: this.journal.chequeBankAccountId,
                    $select: ['bankId'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });

                if (!!bankAccount) {
                    model.bankId = bankAccount.bankId;
                }
            }

            if (this.$params('id')) {
                const partner = await this.$collection('kernel.partners').findOne({
                    _id: this.model.partnerId,
                    $select: ['chequesEndorsable']
                });

                this.isEndorsable = !!partner.chequesEndorsable;
            }

            if (!!model.bankId) {
                this.bank = await this.$collection('kernel.banks').findOne({_id: model.bankId});
            }

            return model;
        },
        async beforeSubmit(model) {
            if (model.endorsable && _.isBoolean(this.isEndorsable) && !this.isEndorsable) {
                throw new Error(
                    this.$t(
                        'This partner promissory notes cannot be endorsed! Please update the details of relevant partner.'
                    )
                );
            }

            if (!this.$params('id')) {
                const isConfirmed = await new Promise(resolve => {
                    this.$program.alert(
                        'confirm',
                        this.$t('Related accounting records will be created and posted. Do you want continue?'),
                        confirmed => {
                            resolve(confirmed);
                        }
                    );
                });

                if (isConfirmed) {
                    return model;
                } else {
                    return false;
                }
            }

            return model;
        },
        async handleChange(model, field) {
            const company = this.$store.getters['session/company'];

            if (field === 'partnerType') {
                this.$set(this.model, 'partnerId', '');

                this.$nextTick(() => {
                    this.partnerIdKey = _.uniqueId('partnerIdKey');
                });
            } else if (field === 'partnerId') {
                await this.updateIsEndorsable();
            } else if (field === 'currencyId') {
                if (model.currencyId) {
                    const currency = await this.$collection('kernel.currencies').findOne({
                        _id: model.currencyId,
                        $select: ['name']
                    });

                    if (_.isObject(currency)) {
                        if (company.currencyId !== model.currencyId) {
                            this.model.currencyRate = await this.$convertCurrency({
                                from: currency.name,
                                to: company.currency.name,
                                value: 1,
                                options: {
                                    date: model.issueDate
                                }
                            });
                        } else {
                            this.model.currencyRate = 1;
                        }
                    }
                } else {
                    this.model.currencyId = company.currencyId;
                    this.model.currencyRate = 1;
                }
            } else if (field === 'endorsable') {
                if (model.endorsable && _.isBoolean(this.isEndorsable) && !this.isEndorsable) {
                    this.model.endorsable = false;

                    this.$program.message(
                        'error',
                        this.$t(
                            'This partner cheques cannot be endorsed! Please update the details of relevant partner.'
                        )
                    );
                }
            } else if (field === 'bankId') {
                this.model.bankBranchName = '';
                this.bank = await this.$collection('kernel.banks').findOne({_id: model.bankId});
            } else if (field === 'city') {
                this.model.district = '';
            }
        },

        updatePartnerSelectParams(params) {
            params.model = {type: this.model.partnerType};

            return params;
        },
        updateContactSelectParams(params, type) {
            if (type === 'create' || type === 'detail') {
                params.model = {type: 'contact'};
                params.partnerId = this.model.partnerId;
            } else if (type === 'list') {
                params.filters = {partnerId: this.model.partnerId, type: 'contact'};
            }

            return params;
        },
        async updateIsEndorsable() {
            if (this.model.partnerId) {
                const partner = await this.$collection('kernel.partners').findOne({
                    _id: this.model.partnerId,
                    $select: ['chequesEndorsable']
                });

                this.isEndorsable = !!partner.chequesEndorsable;

                if (!this.isEndorsable) {
                    this.model.endorsable = false;
                }
            }
        },
        async fetchSuggestions(search, {code: divisionCode}) {
            const model = this.model;
            const company = this.$store.getters['session/company'];

            const query = {};
            query.countryId = company.country._id;
            if (divisionCode === 'district') {
                query.city = model.city;
            }

            search = search.trim();

            if (search) {
                query.$or = [
                    {
                        [divisionCode]: {$regex: toUpper(escapeRegExp(search)), $options: 'i'}
                    },
                    {
                        [divisionCode]: {$regex: toLower(escapeRegExp(search)), $options: 'i'}
                    },
                    {
                        [divisionCode]: {$regex: firstUpper(escapeRegExp(search)), $options: 'i'}
                    }
                ];
            }

            return await this.$rpc('kernel.component.address-suggestions', {
                query,
                divisionCode: divisionCode
            });
        },
        async fetchBankBranchSuggestions(search) {
            const query = {};

            if (_.isPlainObject(this.bank)) {
                query._id = {$in: this.bank.bankBranchIds || []};
            }

            search = search.trim();

            if (search) {
                query.$or = [
                    {
                        name: {$regex: toUpper(escapeRegExp(search)), $options: 'i'}
                    },
                    {
                        name: {$regex: toLower(escapeRegExp(search)), $options: 'i'}
                    },
                    {
                        name: {$regex: firstUpper(escapeRegExp(search)), $options: 'i'}
                    }
                ];
            }

            query.$select = ['name'];

            return (await this.$collection('kernel.bank-branches').find(query)).map(item => item.name);
        }
    },

    async created() {
        this.currencies = await this.$collection('kernel.currencies').find();

        if (!!this.$params('id') && !this.$params('journal')) {
            const cheque = await this.$collection('finance.cheques').findOne({
                _id: this.$params('id'),
                $select: ['status', 'journalId'],
                $populate: ['journal']
            });
            this.$params('tab', cheque.status);
            this.journal = cheque.journal;
        } else {
            this.journal = this.$params('journal');
        }

        this.$collection('kernel.partners').on('all', this.updateIsEndorsable);

        this.tagOptions = (await this.$collection('finance.tags').find({scope: 'cheque'})).map(tag => ({
            value: tag._id,
            label: tag.name
        }));

        this.initialized = true;
    },
    beforeDestroy() {
        this.$collection('kernel.partners').removeListener('all', this.updateIsEndorsable);
    }
};
</script>
