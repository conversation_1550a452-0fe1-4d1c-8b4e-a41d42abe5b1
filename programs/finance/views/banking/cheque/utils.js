import _ from 'lodash';

export function getColumns(vm) {
    const company = vm.$store.getters['session/company'];
    const scopeOptions = [
        {value: '1', label: vm.$setting('system.scope1Label')},
        {value: '2', label: vm.$setting('system.scope2Label')}
    ];

    return [
        {
            field: 'dueDate',
            label: 'Due Date',
            format: 'date',
            sort: 'desc',
            width: 120
        },
        {
            field: 'recordDate',
            label: 'Record Date',
            format: 'date',
            visible: false,
            width: 120
        },
        {
            field: 'issueDate',
            label: 'Issue Date',
            format: 'date',
            width: 120
        },
        {
            field: 'updatedAt',
            label: 'Last transaction date',
            format: 'datetime',
            visible: false,
            width: 180
        },
        {
            field: 'chequeNumber',
            label: 'Cheque Number',
            width: 150
        },
        {
            field: 'chequeTrackingCode',
            label: 'Cheque tracking code',
            width: 150,
            visible: false
        },
        {
            field: 'endorsable',
            label: 'Endorsable',
            width: 120,
            render(params) {
                return _.isObject(params.data) && _.isBoolean(params.data.endorsable)
                    ? params.data.endorsable
                        ? vm.$t('Yes')
                        : vm.$t('No')
                    : '';
            }
        },
        {
            field: 'branch.name',
            label: 'Branch Office',
            hidden: !vm.$setting('system.multiBranch'),
            width: 180
        },
        {
            field: 'code',
            label: 'Voucher No',
            visible: false,
            view: 'accounting.adviser.journal-entries-detail',
            relationParams(params) {
                const data = params.data;

                return {id: data.journalEntryId};
            }
        },
        {
            field: 'journal.name',
            label: 'Account',
            visible: false,
            width: 180
        },
        {
            field: 'partner',
            label: 'Partner',
            subSelect: ['code', 'name'],
            visible: false,
            relationParams(params) {
                const data = params.data;
                const relation = {};

                relation.isVisible =
                    _.isObject(data.partner) && _.isString(data.partner.code) && _.isString(data.partner.name);

                if (relation.isVisible) {
                    relation.view = 'partners.partners-detail';
                    relation.id = data.partnerId;
                    relation.template = '{{code}} - {{name}}';
                }

                return relation;
            },
            width: 180
        },
        {
            field: 'endorsedPartner',
            label: 'Endorsed partner',
            subSelect: ['code', 'name'],
            visible: false,
            relationParams(params) {
                const data = params.data;
                const relation = {};

                relation.isVisible =
                    _.isObject(data.endorsedPartner) &&
                    _.isString(data.endorsedPartner.code) &&
                    _.isString(data.endorsedPartner.name);

                if (relation.isVisible) {
                    relation.view = 'partners.partners-detail';
                    relation.id = data.endorsedPartnerId;
                    relation.template = '{{code}} - {{name}}';
                }

                return relation;
            },
            width: 180
        },
        {
            field: 'financialIdentifier',
            label: 'Financial Identifier',
            width: 120,
            visible: false
        },
        {
            field: 'firstWrittenBy',
            label: 'First Written By',
            with: 150,
            visible: false
        },
        {
                field: 'editedByInfo',
                label: 'Edited by',
                subSelect: ['code', 'name'],
                width: 240,
                relationParams(params) {
                    const data = params.data;
                    const relation = {};

                    relation.isVisible =
                        _.isObject(data.editedByInfo) &&
                        _.isString(data.editedByInfo.code) &&
                        _.isString(data.editedByInfo.name);

                    if (relation.isVisible) {
                        relation.view = 'system.members.users-detail';
                        relation.id = data.editedBy;
                        relation.template = '{{code}} - {{name}}';
                    }

                    return relation;
                }
            },
        {
            field: 'bank.name',
            label: 'Cheque bank',
            with: 150,
            visible: false
        },
        {
            field: 'bankBranchName',
            label: 'Cheque bank branch',
            with: 150,
            visible: false
        },
        {
            field: 'city',
            label: 'City',
            with: 120,
            visible: false
        },
        {
            field: 'district',
            label: 'District',
            with: 120,
            visible: false
        },
        {
            field: 'paymentAccount.name',
            label: 'Payment Account',
            width: 240
        },
        {
            field: 'reference',
            label: 'Reference'
        },
        {
            field: 'description',
            label: 'Description',
            visible: false
        },
        {
            field: 'paymentSlipNo',
            label: 'Payment slip no',
            width: 120,
            visible: false
        },
        {
            field: 'paymentSlipDate',
            label: 'Payment slip date',
            format: 'date',
            width: 150,
            visible: false
        },
        {
            field: 'tagIds',
            label: 'Tags',
            tagsCell: true,
            tagLabels: vm.tagOptions
        },
        {
            field: 'scope',
            label: 'Scope',
            hidden: !vm.$setting('system.scopes'),
            visible: false,
            width: 90,
            valueLabels: scopeOptions
        },
        {
            field: 'currency.name',
            label: 'Currency',
            hidden: !vm.$setting('system.multiCurrency'),
            visible: false,
            width: 120
        },
        {
            field: 'amount',
            label: 'Amount',
            format: 'currency',
            formatOptions(row) {
                if (_.isObject(row) && row.currencyId) {
                    const currency = vm.currencies.find(c => c._id === row.currencyId);
                    let options = {currency: {}};

                    options.currency.symbol = currency.symbol;
                    options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                    return options;
                }

                return {};
            },
            width: 150
        },
        {
            field: 'systemCurrencyAmount',
            label: 'Amount (SC)',
            visible: false,
            hidden: !vm.$setting('system.multiCurrency'),
            format: 'currency',
            formatOptions(row) {
                const currency = company.currency;
                let options = {currency: {}};

                options.currency.symbol = currency.symbol;
                options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                return options;
            },
            width: 120
        },
        {
            headerName: '#',
            field: 'popup-edit-cell',
            width: 35,
            maxWidth: 35,
            pinned: 'right',
            lockPosition: true,
            lockVisible: true,
            lockPinned: true,
            editable: false,
            cellClass: params => {
                if (params.node.rowPinned) {
                    return '';
                }

                return 'popup-edit-cell';
            },
            headerClass: 'popup-edit-header-cell',
            resizable: false,
            suppressNavigable: true,
            suppressMovable: true,
            suppressSizeToFit: true,
            sortable: false,
            suppressMenu: true,
            suppressColumnsToolPanel: true,
            headerComponentParams: {
                template:
                    '<div class="ag-cell-label-container" role="presentation">' +
                    '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                    '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                    `     <span class="ag-header-cell-text" role="columnheader"></span>` +
                    '  </div>' +
                    '</div>'
            },
            cellRenderer(params) {
                if (params.node.rowPinned) {
                    return '';
                }

                return '<i class="fal fa-arrow-right"></i>';
            },
            onCellClicked(params) {
                if (params.node.rowPinned) {
                    return '';
                }

                vm.$shell.openProgram('finance.banking.cheque-item', {
                    id: params.data._id,
                    isPreview: true,
                    tab: params.data.status,
                    journal: vm.journal,
                    currencies: vm.currencies,
                    currencyFormat: vm.currencyFormat,
                    model: {
                        status: params.data.status,
                        isIssued: !!vm.journal.chequesIssued,
                        journalId: vm.journal._id
                    }
                });

                return false;
            }
        }
    ];
}

export function getApplicableScopeFilters(vm) {
    const scopeOptions = [
        {value: '1', label: vm.$setting('system.scope1Label')},
        {value: '2', label: vm.$setting('system.scope2Label')}
    ];

    return [
        {
            field: 'dueDate',
            label: 'Due date',
            type: 'date'
        },
        {
            field: 'recordDate',
            label: 'Record date',
            type: 'date'
        },
        {
            field: 'issueDate',
            label: 'Issue date',
            type: 'date'
        },
        {
            field: 'chequeNumber',
            label: 'Cheque number'
        },
        {
            type: 'boolean',
            field: 'endorsable',
            label: 'Endorsable'
        },
        {
            field: 'branchId',
            label: 'Branch office',
            collection: 'kernel.branches',
            filters: {
                _id: {$in: vm.$user.branchIds},
                $sort: {name: 1}
            },
            condition() {
                return vm.$setting('system.multiBranch');
            }
        },
        {field: 'code', label: 'Voucher no'},
        {
            field: 'partnerType',
            label: 'Partner type',
            translateLabels: true,
            items: [
                {value: 'customer', label: 'Customer'},
                {value: 'vendor', label: 'Vendor'},
                {value: 'employee', label: 'Employee'}
            ]
        },
        {
            field: 'partnerId',
            label: 'Partner',
            collection: 'kernel.partners',
            extraFields: ['code'],
            filters: {$sort: {name: 1}},
            template: '{{code}} - {{name}}'
        },
        {
            field: 'endorsedPartnerId',
            label: 'Endorsed partner',
            collection: 'kernel.partners',
            extraFields: ['code'],
            filters: {$sort: {name: 1}},
            template: '{{code}} - {{name}}'
        },
        {field: 'reference', label: 'Reference'},
        {field: 'chequeTrackingCode', label: 'Cheque tracking code'},
        {field: 'description', label: 'Description'},
        {
            field: 'paymentSlipNo',
            label: 'Payment slip no'
        },
        {
            field: 'paymentSlipDate',
            label: 'Payment slip date',
            type: 'date'
        },
        {
            field: 'paymentAccountId',
            label: 'Payment account',
            collection: 'accounting.journals',
            filters: {$sort: {name: 1}}
        },
        {
            field: 'tagIds',
            label: 'Tags',
            valueLabels: vm.tagOptions
        },
        {
            field: 'currencyId',
            label: 'Currency',
            collection: 'kernel.currencies',
            filters: {$sort: {name: 1}},
            condition() {
                return vm.$setting('system.multiCurrency');
            }
        },
        {
            type: 'decimal',
            field: 'amount',
            label: 'Amount',
            format: 'currency'
        },
        {
            field: 'systemCurrencyAmount',
            label: 'Amount (SC)',
            condition() {
                return vm.$setting('system.multiCurrency');
            },
            format: 'currency'
        },
        {
            field: 'scope',
            label: 'Scope',
            condition() {
                return vm.$setting('system.scopes');
            },
            valueLabels: scopeOptions
        },
        {
            field: 'editedBy',
            label: 'Edited by',
            collection: 'kernel.users',
            extraFields: ['username', 'name'],
            filters: {$sort: {name: 1}},
            template: '{{username}} - {{name}}'
        }
    ];
}
