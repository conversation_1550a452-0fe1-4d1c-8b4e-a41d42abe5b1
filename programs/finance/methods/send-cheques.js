import _ from 'lodash';

export default {
    name: 'send-cheques',
    async action({data}, params) {
        const app = this.app;
        const company = await app.collection('kernel.company').findOne({});
        const currencies = await app.collection('kernel.currencies').find({
            $select: ['name']
        });
        const items = data.items || [];

        if (items.length < 1) return;

        if (items.some(item => !item.move)) {
            throw new app.errors.Unprocessable(
                app.translate('The destination status must be defined on all cheques that will be moved!')
            );
        }

        const cheques = await app.collection('finance.cheques').find({
            _id: {$in: items.map(item => item._id)},
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });
        const partners = await app.collection('kernel.partners').find({
            _id: {$in: cheques.map(cheque => cheque.partnerId)},
            $select: ['_id', 'type', 'currencyId', 'enableLimitChecks'],
            $disableSoftDelete: true,
            $disableActiveCheck: true
        });

        for (const item of items) {

            if (item.move === 'charged') {
                item.dueDate = item.issueDate;
            }

            const cheque = cheques.find(cheque => cheque._id === item._id);
            const partner = partners.find(partner => partner._id === cheque.partnerId);

            const exchangeRates = cheque.exchangeRates ?? [];
            const dueExchangeRatesMap = {};
            await (async () => {
                const payloads = [];
                for (const currency of currencies) {
                    if (currency.name === company.currency.name) {
                        continue;
                    }

                    payloads.push({
                        from: currency.name,
                        to: company.currency.name,
                        value: 1,
                        options: {
                            date: item.dueDate
                        }
                    });
                }

                for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
                    const index = exchangeRates.findIndex(er => er.currencyName === payload.from);

                    if (
                        index !== -1 &&
                        (!_.isFinite(exchangeRates[index].dueRate) || exchangeRates[index].dueRate === 0)
                    ) {
                        exchangeRates[index].dueRate = payload.rate;
                        dueExchangeRatesMap[payload.from] = payload.rate;
                    }
                }
            })();

            if (item.move === 'in-bill-case') {
                const journal = await app.collection('accounting.journals').findOne({
                    _id: data.journalId,
                    $select: ['debitAccountId', 'creditAccountId']
                });

                if (!journal.debitAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Journal debit account is required!'));
                }

                const entry = {items: []};

                // Prepare general data.
                entry.documentNo = item.chequeNumber;
                entry.reference = item.reference;
                entry.description = item.description;
                entry.branchId = item.branchId;
                entry.recordDate = item.recordDate;
                entry.issueDate = item.issueDate;
                entry.dueDate = item.dueDate;
                entry.journalId = data.journalId;
                if (item.chequeTrackingCode) entry.chequeTrackingCode = item.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = item.currentAccountId;
                firstItem.partnerId = cheque.status === 'endorsed' ? cheque.endorsedPartnerId : item.partnerId;
                firstItem.description = item.description;
                firstItem.branchId = item.branchId;
                firstItem.salespersonId = cheque.salespersonId;
                firstItem.currencyId = item.currencyId;
                firstItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    firstItem.creditFC = item.amount;
                    firstItem.credit = item.amount * item.currencyRate;
                } else {
                    firstItem.credit = item.amount;
                }
                entry.items.push(firstItem);

                // Second item.
                const secondItem = {};
                secondItem.accountId = journal.debitAccountId;
                secondItem.partnerId = cheque.status === 'endorsed' ? cheque.endorsedPartnerId : item.partnerId;
                secondItem.description = item.description;
                secondItem.branchId = item.branchId;
                secondItem.salespersonId = cheque.salespersonId;
                secondItem.currencyId = item.currencyId;
                secondItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    secondItem.debitFC = item.amount;
                    secondItem.debit = item.amount * item.currencyRate;
                } else {
                    secondItem.debit = item.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: item._id,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: item._id,
                        journalId: data.journalId,
                        status: item.move,
                        code: voucherNo,
                        amount: item.amount,
                        currencyId: item.currencyId,
                        currencyRate: item.currencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: item.recordDate,
                        issueDate: item.issueDate,
                        dueDate: item.dueDate,
                        reference: item.reference,
                        description: item.description,
                        partnerId: cheque.status === 'endorsed' ? cheque.endorsedPartnerId : item.partnerId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: item._id},
                    {
                        status: 'in-bill-case',
                        code: voucherNo,
                        currentAccountId: journal.debitAccountId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        exchangeRates,
                        dueExchangeRatesMap
                    },
                    {user: params.user}
                );
            } else if (item.move === 'deposited') {
                if (!item.paymentAccountId) {
                    throw new app.errors.Unprocessable(app.translate('Payment account is required!'));
                }

                const paymentJournal = await app.collection('accounting.journals').findOne({
                    _id: item.paymentAccountId
                });

                if (!paymentJournal.chequeCollectionAccountId) {
                    throw new app.errors.Unprocessable(
                        this.translate('Payment journal cheque collection account is required!')
                    );
                }

                const entry = {items: []};

                // Prepare general data.
                entry.documentNo = item.chequeNumber;
                entry.reference = item.reference;
                entry.description = item.description;
                entry.branchId = item.branchId;
                entry.recordDate = item.recordDate;
                entry.issueDate = item.issueDate;
                entry.dueDate = item.dueDate;
                entry.journalId = data.journalId;
                if (item.chequeTrackingCode) entry.chequeTrackingCode = item.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = item.currentAccountId;
                firstItem.partnerId = item.partnerId;
                firstItem.description = item.description;
                firstItem.branchId = item.branchId;
                firstItem.salespersonId = cheque.salespersonId;
                firstItem.currencyId = item.currencyId;
                firstItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    firstItem.creditFC = item.amount;
                    firstItem.credit = item.amount * item.currencyRate;
                } else {
                    firstItem.credit = item.amount;
                }
                entry.items.push(firstItem);

                // First item.
                const secondItem = {};
                secondItem.accountId = paymentJournal.chequeCollectionAccountId;
                secondItem.partnerId = item.partnerId;
                secondItem.description = item.description;
                secondItem.branchId = item.branchId;
                secondItem.salespersonId = cheque.salespersonId;
                secondItem.currencyId = item.currencyId;
                secondItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    secondItem.debitFC = item.amount;
                    secondItem.debit = item.amount * item.currencyRate;
                } else {
                    secondItem.debit = item.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: item._id,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: item._id,
                        journalId: data.journalId,
                        status: item.move,
                        code: voucherNo,
                        amount: item.amount,
                        currencyId: item.currencyId,
                        currencyRate: item.currencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: item.recordDate,
                        issueDate: item.issueDate,
                        dueDate: item.dueDate,
                        reference: item.reference,
                        description: item.description,
                        partnerId: item.partnerId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: item._id},
                    {
                        status: 'deposited',
                        code: voucherNo,
                        currentAccountId: paymentJournal.chequeCollectionAccountId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        exchangeRates,
                        dueExchangeRatesMap
                    },
                    {user: params.user}
                );
            } else if (item.move === 'endorsed') {
                if (!item.partnerId) {
                    throw new app.errors.Unprocessable(app.translate('Partner is required!'));
                }

                const partner = await app.collection('kernel.partners').findOne({
                    _id: item.partnerId,
                    $select: ['accountingAccountId']
                });

                if (!_.isString(partner.accountingAccountId) || !partner.accountingAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Partner accounting account is required!'));
                }

                const entry = {items: []};

                // Prepare general data.
                entry.documentNo = item.chequeNumber;
                entry.reference = item.reference;
                entry.description = item.description;
                entry.branchId = item.branchId;
                entry.recordDate = item.recordDate;
                entry.issueDate = item.issueDate;
                entry.dueDate = item.dueDate;
                entry.journalId = data.journalId;
                if (item.chequeTrackingCode) entry.chequeTrackingCode = item.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = item.currentAccountId;
                firstItem.partnerId = item.partnerId;
                firstItem.description = item.description;
                firstItem.branchId = item.branchId;
                firstItem.salespersonId = cheque.salespersonId;
                firstItem.currencyId = item.currencyId;
                firstItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    firstItem.creditFC = item.amount;
                    firstItem.credit = item.amount * item.currencyRate;
                } else {
                    firstItem.credit = item.amount;
                }
                entry.items.push(firstItem);

                // First item.
                const secondItem = {};
                secondItem.accountId = partner.accountingAccountId;
                secondItem.partnerId = item.partnerId;
                secondItem.description = item.description;
                secondItem.branchId = item.branchId;
                secondItem.salespersonId = cheque.salespersonId;
                secondItem.currencyId = item.currencyId;
                secondItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    secondItem.debitFC = item.amount;
                    secondItem.debit = item.amount * item.currencyRate;
                } else {
                    secondItem.debit = item.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: item._id,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: item._id,
                        journalId: data.journalId,
                        status: item.move,
                        code: voucherNo,
                        amount: item.amount,
                        currencyId: item.currencyId,
                        currencyRate: item.currencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: item.recordDate,
                        issueDate: item.issueDate,
                        dueDate: item.dueDate,
                        reference: item.reference,
                        description: item.description,
                        partnerId: item.partnerId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: item._id},
                    {
                        status: 'endorsed',
                        code: voucherNo,
                        endorsedPartnerId: item.partnerId,
                        currentAccountId: partner.accountingAccountId,
                        journalEntryId: journalEntry._id,
                        exchangeRates,
                        dueExchangeRatesMap
                    },
                    {user: params.user}
                );
            } else if (item.move === 'charged') {
                if (!item.paymentAccountId) {
                    throw new app.errors.Unprocessable(app.translate('Payment account is required!'));
                }

                const paymentJournal = await app.collection('accounting.journals').findOne({
                    _id: item.paymentAccountId
                });

                if (!paymentJournal.debitAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Payment journal debit account is required!'));
                }

                const entry = {items: []};

                // Prepare general data.
                entry.documentNo = item.chequeNumber;
                entry.reference = item.reference;
                entry.description = item.description;
                entry.branchId = item.branchId;
                entry.recordDate = item.recordDate;
                entry.issueDate = item.issueDate;
                entry.dueDate = item.dueDate;
                entry.journalId = data.journalId;
                if (item.chequeTrackingCode) entry.chequeTrackingCode = item.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = item.currentAccountId;
                firstItem.partnerId = item.partnerId;
                firstItem.description = item.description;
                firstItem.branchId = item.branchId;
                firstItem.currencyId = item.currencyId;
                firstItem.salespersonId = cheque.salespersonId;
                firstItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    firstItem.creditFC = item.amount;
                    firstItem.credit = item.amount * item.currencyRate;
                } else {
                    firstItem.credit = item.amount;
                }
                entry.items.push(firstItem);

                // First item.
                const secondItem = {};
                secondItem.accountId = paymentJournal.debitAccountId;
                secondItem.partnerId = item.partnerId;
                secondItem.description = item.description;
                secondItem.branchId = item.branchId;
                secondItem.salespersonId = cheque.salespersonId;
                secondItem.currencyId = item.currencyId;
                secondItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    secondItem.debitFC = item.amount;
                    secondItem.debit = item.amount * item.currencyRate;
                } else {
                    secondItem.debit = item.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: item._id,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: item._id,
                        journalId: data.journalId,
                        status: item.move,
                        code: voucherNo,
                        amount: item.amount,
                        currencyId: item.currencyId,
                        currencyRate: item.currencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: item.recordDate,
                        issueDate: item.issueDate,
                        dueDate: item.dueDate,
                        reference: item.reference,
                        description: item.description,
                        partnerId: item.partnerId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: item._id},
                    {
                        status: 'charged',
                        code: voucherNo,
                        currentAccountId: paymentJournal.debitAccountId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        exchangeRates,
                        dueExchangeRatesMap
                    },
                    {user: params.user}
                );
            } else if (item.move === 'denied') {
                const journal = await app.collection('accounting.journals').findOne({
                    _id: data.journalId,
                    $select: ['deniedChequeAccountId']
                });

                if (!journal.deniedChequeAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Journal denied cheque account is required!'));
                }

                const entry = {items: []};

                // Prepare general data.
                entry.documentNo = item.chequeNumber;
                entry.reference = item.reference;
                entry.description = item.description;
                entry.branchId = item.branchId;
                entry.recordDate = item.recordDate;
                entry.issueDate = item.issueDate;
                entry.dueDate = item.dueDate;
                entry.journalId = data.journalId;
                if (item.chequeTrackingCode) entry.chequeTrackingCode = item.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = item.currentAccountId;
                firstItem.partnerId = item.partnerId;
                firstItem.description = item.description;
                firstItem.branchId = item.branchId;
                firstItem.salespersonId = cheque.salespersonId;
                firstItem.currencyId = item.currencyId;
                firstItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    firstItem.creditFC = item.amount;
                    firstItem.credit = item.amount * item.currencyRate;
                } else {
                    firstItem.credit = item.amount;
                }
                entry.items.push(firstItem);

                // First item.
                const secondItem = {};
                secondItem.accountId = journal.deniedChequeAccountId;
                secondItem.partnerId = item.partnerId;
                secondItem.description = item.description;
                secondItem.branchId = item.branchId;
                secondItem.salespersonId = cheque.salespersonId;
                secondItem.currencyId = item.currencyId;
                secondItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    secondItem.debitFC = item.amount;
                    secondItem.debit = item.amount * item.currencyRate;
                } else {
                    secondItem.debit = item.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: item._id,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: item._id,
                        journalId: data.journalId,
                        status: item.move,
                        code: voucherNo,
                        amount: item.amount,
                        currencyId: item.currencyId,
                        currencyRate: item.currencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: item.recordDate,
                        issueDate: item.issueDate,
                        dueDate: item.dueDate,
                        reference: item.reference,
                        description: item.description,
                        partnerId: item.partnerId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: item._id},
                    {
                        status: 'denied',
                        code: voucherNo,
                        currentAccountId: journal.deniedChequeAccountId,
                        journalEntryId: journalEntry._id,
                        exchangeRates,
                        dueExchangeRatesMap
                    },
                    {user: params.user}
                );
            } else if (item.move === 'refunded') {
                if (!item.partnerId) {
                    throw new app.errors.Unprocessable(app.translate('Partner is required!'));
                }

                const partner = await app.collection('kernel.partners').findOne({
                    _id: item.partnerId,
                    $select: ['accountingAccountId']
                });

                if (!_.isString(partner.accountingAccountId) || !partner.accountingAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Partner accounting account is required!'));
                }

                const entry = {items: []};

                // Prepare general data.
                entry.documentNo = item.chequeNumber;
                entry.reference = item.reference;
                entry.description = item.description;
                entry.branchId = item.branchId;
                entry.recordDate = item.recordDate;
                entry.issueDate = item.issueDate;
                entry.dueDate = item.dueDate;
                entry.journalId = data.journalId;
                if (item.chequeTrackingCode) entry.chequeTrackingCode = item.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = item.currentAccountId;
                firstItem.partnerId = item.partnerId;
                firstItem.description = item.description;
                firstItem.branchId = item.branchId;
                firstItem.salespersonId = cheque.salespersonId;
                firstItem.currencyId = item.currencyId;
                firstItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    firstItem.creditFC = item.amount;
                    firstItem.credit = item.amount * item.currencyRate;
                } else {
                    firstItem.credit = item.amount;
                }
                entry.items.push(firstItem);

                // First item.
                const secondItem = {};
                secondItem.accountId = partner.accountingAccountId;
                secondItem.partnerId = item.partnerId;
                secondItem.description = item.description;
                secondItem.branchId = item.branchId;
                secondItem.salespersonId = cheque.salespersonId;
                secondItem.currencyId = item.currencyId;
                secondItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    secondItem.debitFC = item.amount;
                    secondItem.debit = item.amount * item.currencyRate;
                } else {
                    secondItem.debit = item.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: item._id,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: item._id,
                        journalId: data.journalId,
                        status: item.move,
                        code: voucherNo,
                        amount: item.amount,
                        currencyId: item.currencyId,
                        currencyRate: item.currencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: item.recordDate,
                        issueDate: item.issueDate,
                        dueDate: item.dueDate,
                        reference: item.reference,
                        description: item.description,
                        partnerId: item.partnerId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: item._id},
                    {
                        status: 'refunded',
                        code: voucherNo,
                        currentAccountId: partner.accountingAccountId,
                        journalEntryId: journalEntry._id,
                        exchangeRates,
                        dueExchangeRatesMap
                    },
                    {user: params.user}
                );
            } else if (item.move === 'sent-in-the-bailiffs') {
                const journal = await app.collection('accounting.journals').findOne({
                    _id: data.journalId,
                    $select: ['chequeExecutionAccountId']
                });

                if (!journal.chequeExecutionAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Journal cheque execution account is required!'));
                }

                const entry = {items: []};

                // Prepare general data.
                entry.documentNo = item.chequeNumber;
                entry.reference = item.reference;
                entry.description = item.description;
                entry.branchId = item.branchId;
                entry.recordDate = item.recordDate;
                entry.issueDate = item.issueDate;
                entry.dueDate = item.dueDate;
                entry.journalId = data.journalId;
                if (item.chequeTrackingCode) entry.chequeTrackingCode = item.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = item.currentAccountId;
                firstItem.partnerId = item.partnerId;
                firstItem.description = item.description;
                firstItem.branchId = item.branchId;
                firstItem.salespersonId = cheque.salespersonId;
                firstItem.currencyId = item.currencyId;
                firstItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    firstItem.creditFC = item.amount;
                    firstItem.credit = item.amount * item.currencyRate;
                } else {
                    firstItem.credit = item.amount;
                }
                entry.items.push(firstItem);

                // First item.
                const secondItem = {};
                secondItem.accountId = journal.chequeExecutionAccountId;
                secondItem.partnerId = item.partnerId;
                secondItem.description = item.description;
                secondItem.branchId = item.branchId;
                secondItem.salespersonId = cheque.salespersonId;
                secondItem.currencyId = item.currencyId;
                secondItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    secondItem.debitFC = item.amount;
                    secondItem.debit = item.amount * item.currencyRate;
                } else {
                    secondItem.debit = item.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: item._id,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: item._id,
                        journalId: data.journalId,
                        status: item.move,
                        code: voucherNo,
                        amount: item.amount,
                        currencyId: item.currencyId,
                        currencyRate: item.currencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: item.recordDate,
                        issueDate: item.issueDate,
                        dueDate: item.dueDate,
                        reference: item.reference,
                        description: item.description,
                        partnerId: item.partnerId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: item._id},
                    {
                        status: 'sent-in-the-bailiffs',
                        code: voucherNo,
                        currentAccountId: journal.chequeExecutionAccountId,
                        journalEntryId: journalEntry._id,
                        exchangeRates,
                        dueExchangeRatesMap
                    },
                    {user: params.user}
                );
            } else if (item.move === 'given-guarantee') {
                const journal = await app.collection('accounting.journals').findOne({
                    _id: data.journalId,
                    $select: ['chequeGuaranteeAccountId']
                });

                if (!journal.chequeGuaranteeAccountId) {
                    throw new app.errors.Unprocessable(this.translate('Journal cheque guarantee account is required!'));
                }

                const entry = {items: []};

                // Prepare general data.
                entry.documentNo = item.chequeNumber;
                entry.reference = item.reference;
                entry.description = item.description;
                entry.branchId = item.branchId;
                entry.recordDate = item.recordDate;
                entry.issueDate = item.issueDate;
                entry.dueDate = item.dueDate;
                entry.journalId = data.journalId;
                if (item.chequeTrackingCode) entry.chequeTrackingCode = item.chequeTrackingCode;
                if (_.isPlainObject(params.user)) entry.createdBy = params.user._id;

                // First item.
                const firstItem = {};
                firstItem.accountId = item.currentAccountId;
                firstItem.partnerId = item.partnerId;
                firstItem.description = item.description;
                firstItem.branchId = item.branchId;
                firstItem.salespersonId = cheque.salespersonId;
                firstItem.currencyId = item.currencyId;
                firstItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    firstItem.creditFC = item.amount;
                    firstItem.credit = item.amount * item.currencyRate;
                } else {
                    firstItem.credit = item.amount;
                }
                entry.items.push(firstItem);

                // First item.
                const secondItem = {};
                secondItem.accountId = journal.chequeGuaranteeAccountId;
                secondItem.partnerId = item.partnerId;
                secondItem.description = item.description;
                secondItem.branchId = item.branchId;
                secondItem.salespersonId = cheque.salespersonId;
                secondItem.currencyId = item.currencyId;
                secondItem.scope = item.scope || '1';
                if (item.currencyId !== company.currencyId) {
                    secondItem.debitFC = item.amount;
                    secondItem.debit = item.amount * item.currencyRate;
                } else {
                    secondItem.debit = item.amount;
                }
                entry.items.push(secondItem);

                // Create accounting entry.
                const journalEntry = await app.rpc('accounting.save-journal-entry', {data: entry}, {user: params.user});
                await app.rpc('accounting.post-journal-entry', journalEntry._id, {user: params.user});

                // Get code.
                const voucherNo = (
                    await app.collection('accounting.journal-entries').findOne({
                        _id: journalEntry._id,
                        $select: ['voucherNo']
                    })
                ).voucherNo;

                // Save history.
                const lastHistory = await app.collection('finance.cheques-history').findOne({
                    chequeId: item._id,
                    $sort: {createdAt: -1}
                });
                await app.collection('finance.cheques-history').create(
                    {
                        chequeId: item._id,
                        journalId: data.journalId,
                        status: item.move,
                        code: voucherNo,
                        amount: item.amount,
                        currencyId: item.currencyId,
                        currencyRate: item.currencyRate,
                        no: _.isObject(lastHistory) ? lastHistory.no + 1 : 1,
                        recordDate: item.recordDate,
                        issueDate: item.issueDate,
                        dueDate: item.dueDate,
                        reference: item.reference,
                        description: item.description,
                        partnerId: item.partnerId,
                        journalEntryId: journalEntry._id,
                        paymentAccountId: item.paymentAccountId,
                        ...(_.isPlainObject(params.user) && {editedBy: params.user._id})
                    },
                    {user: params.user}
                );

                // Update cheque.
                await app.collection('finance.cheques').patch(
                    {_id: item._id},
                    {
                        status: 'given-guarantee',
                        code: voucherNo,
                        currentAccountId: journal.chequeGuaranteeAccountId,
                        paymentAccountId: item.paymentAccountId,
                        journalEntryId: journalEntry._id,
                        exchangeRates,
                        dueExchangeRatesMap
                    },
                    {user: params.user}
                );
            } else {
                throw new app.errors.Unprocessable(app.translate('Unknown destination status!'));
            }

            if (item.move === 'charged') {
                const limit = {};

                limit.partnerType = partner.type;
                limit.partnerId = partner._id;
                limit.guaranteeId = cheque.guaranteeId;
                limit.date = app.datetime.local().toJSDate();
                limit.documentCollection = 'finance.cheques';
                limit.documentView = 'finance.banking.cheque.item';
                limit.documentId = cheque._id;
                limit.currencyId = partner.currencyId;
                limit.amount = cheque.amount;

                if (cheque.currencyId !== partner.currencyId) {
                    const documentCurrency = await app.collection('kernel.currencies').findOne({
                        _id: cheque.currencyId,
                        $select: ['name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    const partnerCurrency = await app.collection('kernel.currencies').findOne({
                        _id: partner.currencyId,
                        $select: ['name'],
                        $disableActiveCheck: true,
                        $disableSoftDelete: true
                    });
                    limit.amount = await app.rpc('kernel.common.convert-currency', {
                        from: documentCurrency.name,
                        to: partnerCurrency.name,
                        value: cheque.amount,
                        options: {
                            date: cheque.issueDate
                        }
                    });
                }

                await app.collection('finance.partner-limit-transactions').create(limit);
            } else {
                await app
                    .collection('finance.partner-limit-transactions')
                    .remove({documentId: cheque._id}, {disableSoftDelete: true});
            }
        }
    }
};
