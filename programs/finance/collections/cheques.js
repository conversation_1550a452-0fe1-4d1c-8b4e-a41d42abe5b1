import _ from 'lodash';
import {ObjectId} from 'mongodb';

export default {
    name: 'cheques',
    title: 'Cheques',
    branch: true,
    view: 'finance.banking.cheque-item',
    labelParams: {
        from: 'chequeNumber'
    },
    schema: {
        status: {
            type: 'string',
            label: 'Status',
            default: 'in-bill-case',
            allowed: [
                'in-bill-case',
                'deposited',
                'endorsed',
                'charged',
                'denied',
                'refunded',
                'sent-in-the-bailiffs',
                'given-guarantee',
                'produced',
                'paid',
                'issued-refunded'
            ],
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            index: true,
            unique: true,
            required: false
        },
        journalId: {
            type: 'string',
            label: 'Cheque account',
            index: true
        },
        amount: {
            type: 'decimal',
            label: 'Amount',
            min: 0,
            default: 0,
            index: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency',
            index: true
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1
        },
        partnerType: {
            type: 'string',
            label: 'Partner type',
            allowed: ['customer', 'vendor', 'employee'],
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Partner',
            index: true
        },
        endorsedPartnerId: {
            type: 'string',
            label: 'Endorsed partner',
            required: false,
            index: true
        },
        contactPersonId: {
            type: 'string',
            label: 'Contact person',
            required: false
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now',
            index: true
        },
        issueDate: {
            type: 'date',
            label: 'Issue date',
            default: 'date:now',
            index: true
        },
        dueDate: {
            type: 'date',
            label: 'Due date',
            default: 'date:now',
            index: true
        },
        issuedBy: {
            type: 'string',
            label: 'Issued by',
            index: true,
            required: false
        },
        reference: {
            type: 'string',
            label: 'Reference',
            index: true,
            required: false
        },
        description: {
            type: 'string',
            label: 'Description',
            required: false,
            index: true
        },
        isIssued: {
            type: 'boolean',
            label: 'Is issued',
            default: false
        },
        chequeNumber: {
            type: 'string',
            label: 'Document no',
            index: true
        },
        endorsable: {
            type: 'boolean',
            label: 'Endorsable',
            default: false,
            index: true
        },
        paymentAccountId: {
            type: 'string',
            label: 'Payment account',
            required: false,
            index: true
        },
        currentAccountId: {
            type: 'string',
            label: 'Current account',
            required: false,
            index: true
        },
        countryId: {
            type: 'string',
            label: 'Country',
            index: true
        },
        firstWrittenBy: {
            type: 'string',
            label: 'First written by',
            required: false
        },
        editedBy: {
            type: 'string',
            label: 'Edited by',
            required: false
        },
        financialIdentifier: {
            type: 'string',
            label: 'Financial identifier',
            required: false
        },
        guaranteeId: {
            type: 'string',
            label: 'Guarantee',
            required: false
        },
        bankId: {
            type: 'string',
            label: 'Cheque bank',
            required: false,
            index: true
        },
        bankBranchName: {
            type: 'string',
            label: 'Cheque bank branch',
            required: false
        },
        accountNumber: {
            type: 'string',
            label: 'Account number',
            required: false,
            index: true
        },
        iban: {
            type: 'string',
            label: 'IBAN',
            required: false,
            index: true
        },
        city: {
            type: 'string',
            label: 'City',
            required: false
        },
        district: {
            type: 'string',
            label: 'District',
            required: false
        },
        tagIds: {
            type: ['string'],
            label: 'Tags',
            default: [],
            index: true
        },
        journalEntryId: {
            type: 'string',
            required: false
        },
        systemCurrencyAmount: {
            type: 'decimal',
            default: 0,
            min: 0,
            index: true
        },
        cashFlowItemId: {
            type: 'string',
            label: 'Cash flow item',
            required: false,
            index: true
        },
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false,
            index: true
        },
        salespersonId: {
            type: 'string',
            label: 'Salesperson',
            required: false,
            index: true
        },
        scope: {
            type: 'string',
            label: 'Scope',
            default: '1',
            index: true
        },
        paymentSlipNo: {
            type: 'string',
            label: 'Payment slip no',
            required: false,
            index: true
        },
        paymentSlipDate: {
            type: 'datetime',
            label: 'Payment slip date',
            required: false
        },

        exchangeRates: {
            type: [
                {
                    currencyName: {
                        type: 'string',
                        label: 'Currency'
                    },
                    rate: {
                        type: 'decimal',
                        label: 'Rate',
                        default: 0
                    },
                    dueRate: {
                        type: 'decimal',
                        label: 'Due rate',
                        default: 0
                    }
                }
            ],
            default: []
        },
        exchangeRatesMap: {
            type: 'object',
            blackbox: true,
            required: false
        },
        dueExchangeRatesMap: {
            type: 'object',
            blackbox: true,
            required: false
        },
        exchangeRateStatus: {
            type: 'string',
            label: 'Exchange rate status',
            default: 'none',
            required: false
        },
        exchangeRateDocumentCollection: {
            type: 'string',
            label: 'Exchange rate document collection',
            required: false
        },
        exchangeRateDocumentView: {
            type: 'string',
            label: 'Exchange rate document collection',
            required: false
        },
        exchangeRateDocumentId: {
            type: 'string',
            label: 'Exchange rate document ID',
            required: false
        },
        exchangeRateDocumentCode: {
            type: 'string',
            label: 'Exchange rate document code',
            required: false
        },

        attachments: {
            type: ['string'],
            default: []
        },

        hasBankReconciliation: {
            type: 'boolean',
            default: false,
            index: true
        },
        chequeTrackingCode: {
            type: 'string',
            label: 'Cheque tracking code',
            required: false
        }
    },
    attributes: {
        journal: {
            collection: 'accounting.journals',
            parentField: 'journalId',
            childField: '_id'
        },
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        },
        bank: {
            collection: 'kernel.banks',
            parentField: 'bankId',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        editedByInfo: {
            collection: 'kernel.users',
            parentField: 'editedBy',
            childField: '_id'
        },
        endorsedPartner: {
            collection: 'kernel.partners',
            parentField: 'endorsedPartnerId',
            childField: '_id'
        },
        paymentAccount: {
            collection: 'accounting.journals',
            parentField: 'paymentAccountId',
            childField: '_id'
        },
        country: {
            collection: 'kernel.countries',
            parentField: 'countryId',
            childField: '_id'
        },
        createdByInfo: {
            collection: 'kernel.users',
            parentField: 'createdBy',
            childField: '_id'
        },
        financialProject: {
            collection: 'kernel.financial-projects',
            parentField: 'financialProjectId',
            childField: '_id'
        },
        issuedByInfo: {
            collection: 'kernel.partners',
            parentField: 'issuedBy',
            childField: '_id'
        }
    },
    hooks: {
        after: {
            create: [adjustFields, updateJournalParams, syncCashFlowRecords],
            update: [adjustFields, updateJournalParams, syncCashFlowRecords],
            patch: [adjustFields, updateJournalParams, syncCashFlowRecords],
            remove: [syncCashFlowRecords]
        }
    },
    async searchTerms(document) {
        const app = this.app;
        const values = Object.values(_.pick(document, ['code', 'reference', 'description', 'chequeNumber']));

        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
        }
        if (_.isPlainObject(document.createdByInfo)) {
            values.push(document.createdByInfo.code);
            values.push(document.createdByInfo.name);
        }
        if (_.isPlainObject(document.financialProject)) {
            values.push(document.financialProject.code);
            values.push(document.financialProject.name);
        }

        if (Array.isArray(document.tagIds) && document.tagIds.length > 0) {
            const tags = await app.collection('finance.tags').find({
                scope: 'promissory-note',
                $disableSoftDelete: true,
                $disableActiveCheck: true
            });

            for (const tagId of document.tagIds) {
                const tag = tags.find(tag => tag._id === tagId);

                if (!!tag) {
                    values.push(tag.name);
                }
            }
        }

        return values;
    }
};

async function adjustFields(context) {
    const app = context.app;
    const result = Array.isArray(context.result) ? context.result : [context.result];

    for (const document of result) {
        document.systemCurrencyAmount = app.roundNumber(
            document.amount * document.currencyRate,
            app.setting('system.currencyPrecision')
        );

        await app.db.collection('finance_cheques').updateOne(
            {_id: new ObjectId(document._id)},
            {
                $set: {
                    systemCurrencyAmount: document.systemCurrencyAmount
                }
            },
            {
                collation: {locale: app.config('app.locale')}
            }
        );
    }

    return context;
}

function updateJournalParams(context) {
    (async () => {
        const result = Array.isArray(context.result) ? context.result[0] : context.result;
        const journalId = result.journalId;
        const report = await context.app.collection('finance.cheques').aggregate([
            {
                $match: {
                    journalId: journalId
                }
            },
            {
                $group: {
                    _id: '$status',
                    amount: {
                        $sum: {$multiply: ['$amount', '$currencyRate']}
                    },
                    amountFC: {$sum: '$amount'}
                }
            },
            {
                $project: {_id: 0, amount: 1, amountFC: 1, status: '$_id'}
            }
        ]);
        const data = {
            'in-bill-case': {amount: 0, amountFC: 0},
            deposited: {amount: 0, amountFC: 0},
            endorsed: {amount: 0, amountFC: 0},
            charged: {amount: 0, amountFC: 0},
            denied: {amount: 0, amountFC: 0},
            refunded: {amount: 0, amountFC: 0},
            'sent-in-the-bailiffs': {amount: 0, amountFC: 0},
            'given-guarantee': {amount: 0, amountFC: 0},
            produced: {amount: 0, amountFC: 0},
            paid: {amount: 0, amountFC: 0},
            'issued-refunded': {amount: 0, amountFC: 0}
        };
        for (const r of report) {
            data[r.status].amount = r.amount;
            data[r.status].amountFC = r.amountFC;
        }

        await context.app.collection('accounting.journals').patch(
            {_id: journalId},
            {
                'params.cheque': data
            }
        );
    })();

    return context;
}

async function syncCashFlowRecords(context) {
    // noinspection ES6MissingAwait
    (async () => {
        for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
            await context.app.rpc('finance.cash-flow-sync-records', {
                collection: 'finance.cheques',
                documentId: result._id
            });
        }
    })();

    return context;
}
