import _ from 'lodash';
import {rawMongoQuery} from 'framework/helpers';
import fastCopy from 'fast-copy';
import {ObjectId} from 'mongodb';

export default {
    name: 'get-last-balance-before-transaction',
    async action({transactionId, query, issueDate, currencyId}, params) {
        const app = this.app;
        const transactionsCollection = app.collection('accounting.transactions');
        let initialBalance = 0;
        let initialBalanceQuery = {};

        if (!!query && !_.isEmpty(query)) {
            initialBalanceQuery = fastCopy(rawMongoQuery(query));

            for (const df of ['recordDate', 'issueDate', 'dueDate', 'createdAt', 'updatedAt']) {
                if (!_.isUndefined(initialBalanceQuery[df])) {
                    delete initialBalanceQuery[df];
                } else if (Array.isArray(initialBalanceQuery.$and)) {
                    initialBalanceQuery.$and = initialBalanceQuery.$and.filter(q => !_.isObject(q[df]));
                }
            }
        }

        if (!!issueDate && _.isDate(issueDate)) {
            initialBalanceQuery.issueDate = {$lt: issueDate};
        } else {
            initialBalanceQuery._id = {$lt: new ObjectId(transactionId)};
        }
        if (!!currencyId) {
            initialBalanceQuery.currencyId = currencyId;
        }
        const initialBalancePipeline = [
            {$match: initialBalanceQuery},
            {
                $group: {
                    _id: null,
                    debit: {$sum: '$debit'},
                    credit: {$sum: '$credit'},
                    debitFC: {$sum: '$debitFC'},
                    creditFC: {$sum: '$creditFC'}
                }
            },
            {
                $project: {
                    _id: 0,
                    debit: 1,
                    credit: 1,
                    debitFC: 1,
                    creditFC: 1,
                    balance: {$subtract: ['$debit', '$credit']},
                    balanceFC: {$subtract: ['$debitFC', '$creditFC']}
                }
            }
        ];

        const ir = await transactionsCollection.aggregate(initialBalancePipeline);
        if (Array.isArray(ir) && ir.length > 0) {
            if (!!currencyId) {
                initialBalance = ir[0].balanceFC;
            } else {
                initialBalance = ir[0].balance;
            }
        }

        return initialBalance;
    }
};
