import syncSession from './session';
import initChat from './chat';
import initRoutes from './../routes';

export default function (app) {
    app.on('socketio-initialized', io => {
        io.on('connection', async socket => {
            const {user, session} = await syncSession(app, socket, 'connection');
            if (!user) return;

            const removeChatListeners = await initChat(app, socket, user, session);

            // Emit
            // console.log(socket.handshake.headers);
            // socket.emit('mobile-msg', 12);

            socket.on('disconnect', () => {
                syncSession(app, socket, 'disconnection');

                removeChatListeners();
            });
        });
    });

    // Routes
    initRoutes(app);
}
